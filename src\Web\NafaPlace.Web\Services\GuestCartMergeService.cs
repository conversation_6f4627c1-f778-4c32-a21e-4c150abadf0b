using Microsoft.JSInterop;
using NafaPlace.Web.Models.Cart;

namespace NafaPlace.Web.Services
{
    public interface IGuestCartMergeService
    {
        Task<bool> MergeGuestCartOnLoginAsync(string authenticatedUserId);
        Task ClearGuestCartAsync();
    }

    public class GuestCartMergeService : IGuestCartMergeService
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ICartService _cartService;

        public GuestCartMergeService(IJSRuntime jsRuntime, ICartService cartService)
        {
            _jsRuntime = jsRuntime;
            _cartService = cartService;
        }

        public async Task<bool> MergeGuestCartOnLoginAsync(string authenticatedUserId)
        {
            try
            {
                Console.WriteLine($"🔄 DEBUG GuestCartMerge: Début de la fusion pour l'utilisateur {authenticatedUserId}");

                // Récupérer l'ID invité du localStorage
                var guestId = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
                Console.WriteLine($"🔄 DEBUG GuestCartMerge: GuestId récupéré: {guestId}");

                if (string.IsNullOrEmpty(guestId))
                {
                    Console.WriteLine("🔄 DEBUG GuestCartMerge: Pas de guestId trouvé");
                    return false; // Pas de panier invité à fusionner
                }

                // Récupérer le panier invité
                var guestCart = await _cartService.GetCartAsync(guestId);
                Console.WriteLine($"🔄 DEBUG GuestCartMerge: Panier invité récupéré - Items: {guestCart?.Items?.Count ?? 0}");

                if (guestCart?.Items?.Any() != true)
                {
                    Console.WriteLine("🔄 DEBUG GuestCartMerge: Panier invité vide, nettoyage");
                    // Pas d'articles dans le panier invité
                    await ClearGuestCartAsync();
                    return false;
                }

                // Récupérer le panier de l'utilisateur authentifié
                var userCart = await _cartService.GetCartAsync(authenticatedUserId);
                Console.WriteLine($"🔄 DEBUG GuestCartMerge: Panier utilisateur récupéré - Items: {userCart?.Items?.Count ?? 0}");

                // Fusionner les articles du panier invité dans le panier utilisateur
                Console.WriteLine($"🔄 DEBUG GuestCartMerge: Début de la fusion de {guestCart.Items.Count} articles");
                foreach (var guestItem in guestCart.Items)
                {
                    Console.WriteLine($"🔄 DEBUG GuestCartMerge: Fusion article - ProductId: {guestItem.ProductId}, Quantity: {guestItem.Quantity}");

                    var cartItem = new CartItemCreateDto
                    {
                        ProductId = guestItem.ProductId,
                        ProductName = guestItem.ProductName,
                        Price = guestItem.UnitPrice,
                        Quantity = guestItem.Quantity,
                        VariantId = guestItem.VariantId,
                        VariantName = guestItem.VariantName
                    };

                    await _cartService.AddItemToCartAsync(authenticatedUserId, cartItem);
                    Console.WriteLine($"✅ DEBUG GuestCartMerge: Article ajouté avec succès - ProductId: {guestItem.ProductId}");
                }

                // Nettoyer le panier invité
                Console.WriteLine("🔄 DEBUG GuestCartMerge: Nettoyage du panier invité");
                await _cartService.ClearCartAsync(guestId);
                await ClearGuestCartAsync();

                Console.WriteLine("✅ DEBUG GuestCartMerge: Fusion terminée avec succès");
                return true; // Fusion réussie
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ DEBUG GuestCartMerge: Erreur lors de la fusion des paniers: {ex.Message}");
                Console.WriteLine($"❌ DEBUG GuestCartMerge: Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        public async Task ClearGuestCartAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "guestUserId");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du nettoyage du panier invité: {ex.Message}");
            }
        }
    }
}
