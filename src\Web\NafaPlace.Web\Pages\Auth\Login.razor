@page "/auth/login"
@using NafaPlace.Web.Models.Auth
@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Components
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject ILocalStorageService LocalStorage

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <img src="/images/nafaplace-gradient-modern.svg" alt="NafaPlace" height="60" class="mb-3">
                        <h3 class="fw-bold">Connexion</h3>
                        <p class="text-muted">Accédez à votre compte NafaPlace</p>
                    </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            @errorMessage
                        </div>
                    }

                    <EditForm Model="loginRequest" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <InputText id="email" class="form-control" @bind-Value="loginRequest.Email" placeholder="<EMAIL>" />
                            </div>
                            <ValidationMessage For="@(() => loginRequest.Email)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <label for="password" class="form-label">Mot de passe</label>
                                <a href="/auth/forgot-password" class="small text-decoration-none">Mot de passe oublié?</a>
                            </div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <InputText id="password" type="password" class="form-control" @bind-Value="loginRequest.Password" placeholder="Votre mot de passe" />
                            </div>
                            <ValidationMessage For="@(() => loginRequest.Password)" class="text-danger" />
                        </div>

                        <div class="mb-4 form-check">
                            <InputCheckbox id="rememberMe" class="form-check-input" @bind-Value="loginRequest.RememberMe" />
                            <label class="form-check-label" for="rememberMe">Se souvenir de moi</label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 py-2 mb-3" disabled="@isLoading">
                            @if (isLoading)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                <span>Connexion en cours...</span>
                            }
                            else
                            {
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                <span>Se connecter</span>
                            }
                        </button>
                    </EditForm>

                    <div class="text-center mt-4">
                        <p class="mb-0">Vous n'avez pas de compte?</p>
                        <a href="/auth/register" class="btn btn-outline-primary mt-2 w-100">
                            <i class="bi bi-person-plus me-2"></i>Créer un compte
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginRequest loginRequest = new LoginRequest();
    private bool isLoading = false;
    private string errorMessage = string.Empty;

    [Parameter]
    [SupplyParameterFromQuery]
    public string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Vérifier si l'utilisateur est déjà connecté
        var token = await LocalStorage.GetItemAsStringAsync("authToken");
        if (!string.IsNullOrEmpty(token))
        {
            // Si déjà connecté, rediriger vers l'URL de retour ou la page d'accueil
            var redirectUrl = !string.IsNullOrEmpty(ReturnUrl) ? ReturnUrl : "/";
            NavigationManager.NavigateTo(redirectUrl);
        }
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            Console.WriteLine("Tentative de connexion...");

            var result = await AuthService.LoginAsync(loginRequest);

            if (result != null && result.Success)
            {
                Console.WriteLine("Connexion réussie, redirection vers la page d'origine");
                // Rediriger vers l'URL de retour ou la page d'accueil par défaut
                var redirectUrl = !string.IsNullOrEmpty(ReturnUrl) ? ReturnUrl : "/";
                NavigationManager.NavigateTo(redirectUrl, true);
            }
            else if (result != null && !string.IsNullOrEmpty(result.Token))
            {
                Console.WriteLine("Token reçu, redirection vers la page d'origine");
                // Rediriger vers l'URL de retour ou la page d'accueil par défaut
                var redirectUrl = !string.IsNullOrEmpty(ReturnUrl) ? ReturnUrl : "/";
                NavigationManager.NavigateTo(redirectUrl, true);
            }
            else
            {
                Console.WriteLine("Échec de la connexion: " + (result?.Message ?? "Erreur inconnue"));
                errorMessage = result?.Message ?? "Échec de la connexion. Veuillez vérifier vos identifiants.";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la connexion: {ex.Message}");
            errorMessage = "Une erreur s'est produite lors de la connexion. Veuillez réessayer.";
        }
        finally
        {
            isLoading = false;
        }
    }
}
