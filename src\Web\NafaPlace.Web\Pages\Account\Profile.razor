@page "/account/profile"
@using Microsoft.AspNetCore.Components.Authorization
@using NafaPlace.Web.Models.Auth
@using NafaPlace.Web.Services
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@inject ILocalStorageService LocalStorage
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>NafaPlace - Mon Profil</PageTitle>

<AuthorizeView Context="authContext">
    <Authorized>
        <div class="container py-5">
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement de votre profil...</p>
                </div>
            }
            else if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    @errorMessage
                    <button type="button" class="btn btn-primary mt-3" @onclick="ReloadProfile">R<PERSON>sayer</button>
                </div>
            }
            else
            {
                @if (showSuccessMessage)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        @successMessage
                        <button type="button" class="btn-close" @onclick="() => showSuccessMessage = false" aria-label="Close"></button>
                    </div>
                }
                
                <div class="row">
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-body text-center">
                                <img src="@(string.IsNullOrEmpty(profileImageUrl) ? "/images/default-avatar.png" : profileImageUrl)" 
                                     alt="avatar" class="rounded-circle img-fluid" style="width: 150px;">
                                <h5 class="my-3">@user.FirstName @user.LastName</h5>
                                <p class="text-muted mb-1">@GetUserRole()</p>
                                <p class="text-muted mb-4">@user.Email</p>
                                <div class="d-flex justify-content-center mb-2">
                                    <button type="button" class="btn btn-primary" @onclick="() => editMode = true">Modifier le profil</button>
                                    <button type="button" class="btn btn-outline-primary ms-2" @onclick="() => showChangePasswordModal = true">
                                        <i class="fas fa-key me-1"></i>Changer le mot de passe
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-4 mb-lg-0">
                            <div class="card-body p-0">
                                <ul class="list-group list-group-flush rounded-3">
                                    <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                        <i class="fas fa-globe fa-lg text-warning"></i>
                                        <p class="mb-0">Compte créé le @creationDate.ToShortDateString()</p>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                        <i class="fas fa-shopping-bag fa-lg" style="color: #333333;"></i>
                                        <p class="mb-0">0 commandes</p>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                        <i class="fas fa-heart fa-lg" style="color: #ff6b6b;"></i>
                                        <p class="mb-0">0 articles favoris</p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-8">
                        @if (editMode)
                        {
                            <div class="card mb-4">
                                <div class="card-body">
                                    <EditForm Model="@editUser" OnValidSubmit="UpdateProfile">
                                        <DataAnnotationsValidator />
                                        <ValidationSummary />
                                        
                                        <div class="row mb-3">
                                            <div class="col-sm-3">
                                                <h6 class="mb-0">Prénom</h6>
                                            </div>
                                            <div class="col-sm-9 text-secondary">
                                                <InputText @bind-Value="editUser.FirstName" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-sm-3">
                                                <h6 class="mb-0">Nom</h6>
                                            </div>
                                            <div class="col-sm-9 text-secondary">
                                                <InputText @bind-Value="editUser.LastName" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-sm-3">
                                                <h6 class="mb-0">Email</h6>
                                            </div>
                                            <div class="col-sm-9 text-secondary">
                                                <InputText @bind-Value="editUser.Email" class="form-control" disabled />
                                                <small class="text-muted">L'email ne peut pas être modifié</small>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-sm-3">
                                                <h6 class="mb-0">Téléphone</h6>
                                            </div>
                                            <div class="col-sm-9 text-secondary">
                                                <InputText @bind-Value="editUser.PhoneNumber" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-sm-3">
                                                <h6 class="mb-0">Photo de profil</h6>
                                            </div>
                                            <div class="col-sm-9 text-secondary">
                                                <InputText @bind-Value="profileImageUrl" class="form-control" placeholder="URL de l'image" />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-9 text-secondary">
                                                <button type="submit" class="btn btn-primary px-4">Enregistrer</button>
                                                <button type="button" class="btn btn-outline-secondary px-4 ms-2" @onclick="CancelEdit">Annuler</button>
                                            </div>
                                        </div>
                                    </EditForm>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <h6 class="mb-0">Nom complet</h6>
                                        </div>
                                        <div class="col-sm-9 text-secondary">
                                            @user.FirstName @user.LastName
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <h6 class="mb-0">Email</h6>
                                        </div>
                                        <div class="col-sm-9 text-secondary">
                                            @user.Email
                                            @if (user.EmailConfirmed)
                                            {
                                                <span class="badge bg-success ms-2">Vérifié</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning ms-2">Non vérifié</span>
                                            }
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <h6 class="mb-0">Téléphone</h6>
                                        </div>
                                        <div class="col-sm-9 text-secondary">
                                            @(string.IsNullOrEmpty(user.PhoneNumber) ? "Non renseigné" : user.PhoneNumber)
                                            @if (user.PhoneNumberConfirmed)
                                            {
                                                <span class="badge bg-success ms-2">Vérifié</span>
                                            }
                                            else if (!string.IsNullOrEmpty(user.PhoneNumber))
                                            {
                                                <span class="badge bg-warning ms-2">Non vérifié</span>
                                            }
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <h6 class="mb-0">Rôle</h6>
                                        </div>
                                        <div class="col-sm-9 text-secondary">
                                            @GetUserRole()
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4 mb-md-0">
                                    <div class="card-body">
                                        <p class="mb-4"><span class="text-primary font-italic me-1">Activité</span> Statistiques récentes</p>
                                        <p class="mb-1" style="font-size: .77rem;">Dernière connexion</p>
                                        <div class="progress rounded" style="height: 5px;">
                                            <div class="progress-bar" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <p class="mt-4 mb-1" style="font-size: .77rem;">Commandes complétées</p>
                                        <div class="progress rounded" style="height: 5px;">
                                            <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-4 mb-md-0">
                                    <div class="card-body">
                                        <p class="mb-4"><span class="text-primary font-italic me-1">Sécurité</span> Paramètres du compte</p>
                                        <p class="mb-1" style="font-size: .77rem;">Authentification à deux facteurs</p>
                                        <div class="progress rounded" style="height: 5px;">
                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <p class="mt-4 mb-1" style="font-size: .77rem;">Notifications de sécurité</p>
                                        <div class="progress rounded" style="height: 5px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </Authorized>
    <NotAuthorized>
        <div class="container py-5 text-center">
            <h1>Accès non autorisé</h1>
            <p class="lead">Vous devez être connecté pour accéder à cette page.</p>
            <a href="/auth/login?returnUrl=@Uri.EscapeDataString("/account/profile")" class="btn btn-primary">Se connecter</a>
        </div>
    </NotAuthorized>
</AuthorizeView>

<!-- Modal de changement de mot de passe -->
@if (showChangePasswordModal)
{
    <div class="modal fade show" id="changePasswordModal" tabindex="-1" style="display: block; background-color: rgba(0,0,0,0.5);" aria-modal="true" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Changer le mot de passe</h5>
                    <button type="button" class="btn-close" @onclick="CloseChangePasswordModal" aria-label="Close"></button>
                </div>
                <EditForm Model="@changePasswordRequest" OnValidSubmit="HandleChangePasswordSubmit">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        @if (passwordChangeError)
                        {
                            <div class="alert alert-danger" role="alert">
                                @passwordChangeErrorMessage
                            </div>
                        }
                        
                        <div class="mb-3">
                            <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                            <InputText type="password" class="form-control" id="currentPassword" @bind-Value="changePasswordRequest.CurrentPassword" />
                            <ValidationMessage For="@(() => changePasswordRequest.CurrentPassword)" />
                        </div>
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                            <InputText type="password" class="form-control" id="newPassword" @bind-Value="changePasswordRequest.NewPassword" />
                            <ValidationMessage For="@(() => changePasswordRequest.NewPassword)" />
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirmer le nouveau mot de passe</label>
                            <InputText type="password" class="form-control" id="confirmPassword" @bind-Value="changePasswordRequest.ConfirmPassword" />
                            <ValidationMessage For="@(() => changePasswordRequest.ConfirmPassword)" />
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseChangePasswordModal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Changer le mot de passe</button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}

@code {
    private UserDto user = new UserDto();
    private UserDto editUser = new UserDto();
    private bool editMode = false;
    private string profileImageUrl = string.Empty;
    private DateTime creationDate = DateTime.Now;
    private bool isLoading = true;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool showSuccessMessage = false;
    private bool showChangePasswordModal = false;
    private ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest();
    private bool passwordChangeError = false;
    private string passwordChangeErrorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            isLoading = true;
            user = await AuthService.GetCurrentUserAsync();
            
            if (user != null && user.IsAuthenticated)
            {
                // Copier les données pour l'édition
                editUser = new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber,
                    Roles = user.Roles,
                    EmailConfirmed = user.EmailConfirmed,
                    PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                    IsAuthenticated = user.IsAuthenticated
                };
                
                // Récupérer l'URL de l'image de profil depuis le localStorage si disponible
                var savedImageUrl = await LocalStorage.GetItemAsStringAsync("profileImageUrl");
                if (!string.IsNullOrEmpty(savedImageUrl))
                {
                    profileImageUrl = savedImageUrl;
                }
            }
            else
            {
                NavigationManager.NavigateTo("/auth/login?returnUrl=" + Uri.EscapeDataString("/account/profile"));
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors du chargement du profil: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetUserRole()
    {
        if (user.Roles == null || !user.Roles.Any())
        {
            return "Client";
        }
        
        if (user.Roles.Contains("Admin"))
        {
            return "Administrateur";
        }
        else if (user.Roles.Contains("Seller"))
        {
            return "Vendeur";
        }
        else
        {
            return "Client";
        }
    }

    private async Task UpdateProfile()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            Console.WriteLine($"UpdateProfile: Tentative de mise à jour du profil pour {editUser.Email}");
            
            // Sauvegarder l'URL de l'image de profil dans le localStorage
            if (!string.IsNullOrEmpty(profileImageUrl))
            {
                await LocalStorage.SetItemAsStringAsync("profileImageUrl", profileImageUrl);
                Console.WriteLine($"UpdateProfile: URL de l'image de profil sauvegardée: {profileImageUrl}");
            }
            
            // Appeler le service pour mettre à jour le profil
            var result = await AuthService.UpdateProfileAsync(editUser);
            
            if (result != null)
            {
                Console.WriteLine("UpdateProfile: Profil mis à jour avec succès");
                
                // Mettre à jour l'objet user avec les nouvelles valeurs
                user = result;
                
                // Désactiver le mode édition
                editMode = false;
                
                // Afficher un message de succès
                successMessage = "Profil mis à jour avec succès!";
                showSuccessMessage = true;
                
                // Masquer le message de succès après 3 secondes
                await Task.Delay(3000);
                showSuccessMessage = false;
                StateHasChanged();
            }
            else
            {
                Console.WriteLine("UpdateProfile: Échec de la mise à jour du profil");
                errorMessage = "Échec de la mise à jour du profil. Veuillez réessayer.";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du profil: {ex.Message}");
            errorMessage = $"Erreur lors de la mise à jour du profil: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private void CancelEdit()
    {
        // Réinitialiser les données d'édition
        editUser = new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            PhoneNumber = user.PhoneNumber,
            Roles = user.Roles,
            EmailConfirmed = user.EmailConfirmed,
            PhoneNumberConfirmed = user.PhoneNumberConfirmed,
            IsAuthenticated = user.IsAuthenticated
        };
        
        // Désactiver le mode édition
        editMode = false;
    }

    private async Task ReloadProfile()
    {
        await OnInitializedAsync();
    }

    private void CloseChangePasswordModal()
    {
        showChangePasswordModal = false;
        passwordChangeError = false;
        passwordChangeErrorMessage = string.Empty;
        changePasswordRequest = new ChangePasswordRequest();
    }
    
    private async Task HandleChangePasswordSubmit()
    {
        try
        {
            passwordChangeError = false;
            var result = await AuthService.ChangePasswordAsync(changePasswordRequest);
            
            if (result)
            {
                showChangePasswordModal = false;
                changePasswordRequest = new ChangePasswordRequest();
                successMessage = "Votre mot de passe a été modifié avec succès.";
                showSuccessMessage = true;
            }
            else
            {
                passwordChangeError = true;
                passwordChangeErrorMessage = "Échec de la modification du mot de passe. Veuillez vérifier votre mot de passe actuel et réessayer.";
            }
        }
        catch (Exception ex)
        {
            passwordChangeError = true;
            passwordChangeErrorMessage = $"Une erreur s'est produite: {ex.Message}";
        }
    }
}
