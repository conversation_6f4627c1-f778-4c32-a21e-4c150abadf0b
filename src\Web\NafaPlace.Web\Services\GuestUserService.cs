using Microsoft.JSInterop;

namespace NafaPlace.Web.Services
{
    public interface IGuestUserService
    {
        Task<string> GetOrCreateGuestUserIdAsync();
        Task ClearGuestUserIdAsync();
    }

    public class GuestUserService : IGuestUserService
    {
        private readonly IJSRuntime _jsRuntime;

        public GuestUserService(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public async Task<string> GetOrCreateGuestUserIdAsync()
        {
            try
            {
                var guestId = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
                
                if (string.IsNullOrEmpty(guestId))
                {
                    guestId = $"guest_{Guid.NewGuid():N}";
                    await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
                    Console.WriteLine($"🔍 DEBUG GuestUserService: Nouvel ID invité créé: {guestId}");
                }
                else
                {
                    Console.WriteLine($"🔍 DEBUG GuestUserService: ID invité existant récupéré: {guestId}");
                }
                
                return guestId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ DEBUG GuestUserService: Erreur lors de la gestion de l'ID invité: {ex.Message}");
                return $"guest_{Guid.NewGuid():N}";
            }
        }

        public async Task ClearGuestUserIdAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "guestUserId");
                Console.WriteLine("🔍 DEBUG GuestUserService: ID invité supprimé");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ DEBUG GuestUserService: Erreur lors de la suppression de l'ID invité: {ex.Message}");
            }
        }
    }
}
